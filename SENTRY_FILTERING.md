# Sentry Filtering Configuration

This document explains how to control when Sentry sends logs to its API through comprehensive filtering.

## Overview

Sentry is now manually initialized in `MyApplication.kt` with comprehensive filtering applied to all events before they are sent to Sentry's servers. Auto-initialization is disabled in AndroidManifest.xml.

## Configuration Location

All filtering controls are located in `app/src/main/java/so/appio/app/MySentryOptionsConfig.kt` (renamed to `SentryFilterConfig`) in the companion object constants:

```kotlin
// Control flags for different types of logging
const val ENABLE_DEBUG_LOGS = false // Debug builds
const val ENABLE_RELEASE_LOGS = true // Release builds
const val ENABLE_API_ERROR_LOGS = true // API errors
const val ENABLE_DATABASE_ERROR_LOGS = true // Database errors
const val ENABLE_WIDGET_ERROR_LOGS = true // Widget errors
const val ENABLE_CRASH_LOGS = true // Uncaught exceptions
const val ENABLE_PERFORMANCE_LOGS = true // Performance events

// Minimum log level (DEBUG, INFO, WARNING, ERROR, FATAL)
val MINIMUM_LOG_LEVEL = SentryLevel.WARNING
```

## How It Works

### 1. Manual Initialization with Custom Filtering
- Auto-initialization is disabled in AndroidManifest.xml (`io.sentry.auto-init = false`)
- Sentry is manually initialized in `MyApplication.kt` with filtering
- All events are filtered before being sent to Sentry servers

### 2. Event Filtering
The `SentryFilterConfig.filterSentryEvent()` method filters events based on:
- **Build Type**: Different rules for debug vs release builds
- **Log Level**: Only events above `MINIMUM_LOG_LEVEL` are sent
- **Error Type**: Filter by specific error categories (API, database, widget, etc.)
- **Exception Type**: Filter out known exceptions (like `getSplashScreen` compatibility issues)

### 3. Breadcrumb Filtering
The `SentryFilterConfig.filterSentryBreadcrumb()` method filters breadcrumbs (user interaction traces) based on build type and logging preferences.

## Common Use Cases

### Disable All Logging in Debug Builds
```kotlin
const val ENABLE_DEBUG_LOGS = false
```

### Disable All Logging in Release Builds
```kotlin
const val ENABLE_RELEASE_LOGS = false
```

### Only Send Critical Errors
```kotlin
val MINIMUM_LOG_LEVEL = SentryLevel.FATAL
const val ENABLE_API_ERROR_LOGS = false
const val ENABLE_DATABASE_ERROR_LOGS = false
const val ENABLE_WIDGET_ERROR_LOGS = false
```

### Disable Specific Error Types
```kotlin
const val ENABLE_API_ERROR_LOGS = false // No API errors
const val ENABLE_WIDGET_ERROR_LOGS = false // No widget errors
```

### Development Mode (No Sentry Logging)
```kotlin
const val ENABLE_DEBUG_LOGS = false
const val ENABLE_RELEASE_LOGS = false
```

## Testing the Filtering

To test that filtering is working:

1. **Enable debug logging** temporarily:
   ```kotlin
   const val ENABLE_DEBUG_LOGS = true
   ```

2. **Check Android logs** for filtering messages:
   ```bash
   adb logcat | grep "SentryFilterConfig"
   ```

3. **Trigger test events** and verify they're filtered:
   - API errors: Make a network request that fails
   - Database errors: Trigger a database operation that fails
   - Crashes: Throw an uncaught exception

## Advanced Filtering

### Custom Exception Filtering
Add more exception types to ignore in `shouldIgnoreException()`:

```kotlin
private fun shouldIgnoreException(exception: Throwable): Boolean {
    return when (exception) {
        is IllegalStateException -> {
            exception.message?.contains("getSplashScreen") == true
        }
        is NetworkException -> true // Ignore all network exceptions
        is CustomException -> {
            // Custom logic for your exceptions
            exception.code == "IGNORE_THIS"
        }
        else -> false
    }
}
```

### Message-Based Filtering
Filter events based on message content:

```kotlin
// In filterSentryEvent()
if (event.message?.message?.contains("ignore_this_error") == true) {
    return null
}
```

## Important Notes

1. **Filtering happens before sending**: Events are filtered locally, so they never reach Sentry servers
2. **Performance impact**: Minimal - filtering happens in memory before network calls
3. **Debug logging**: Enable `ENABLE_DEBUG_LOGS` to see what's being filtered in Android logs
4. **Fallback behavior**: If filtering fails, events are allowed through to prevent losing critical errors

## Monitoring Filter Effectiveness

Check Android logs to see filtering in action:
```bash
# See all Sentry filtering activity
adb logcat | grep "SentryFilterConfig"

# See what events are being allowed
adb logcat | grep "Allowing event"

# See what events are being filtered out
adb logcat | grep "Filtering out"
```
