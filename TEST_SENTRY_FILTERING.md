# Testing Sentry Filtering

This document provides instructions for testing that Sentry filtering is working correctly.

## Quick Test

1. **Enable debug logging** temporarily in `SentryFilterConfig.kt`:
   ```kotlin
   const val ENABLE_DEBUG_LOGS = true
   ```

2. **Build and run the app**:
   ```bash
   ./gradlew app:installDebug
   ```

3. **Monitor Android logs** for filtering activity:
   ```bash
   adb logcat | grep -E "(SentryFilterConfig|Sentry)"
   ```

4. **Trigger test events** in the app:
   - Navigate through screens (generates breadcrumbs)
   - Force an error (if you have test buttons)
   - Use network features (generates API events)

## Expected Log Output

You should see logs like:
```
D/SentryFilterConfig: Filtering out event in debug build (debug logs disabled)
D/SentryFilterConfig: Allowing event: 12345-67890 (api_error)
D/MyApplication: Sentry initialization completed with filtering enabled
```

## Test Different Filtering Scenarios

### Test 1: Disable All Debug Logs
```kotlin
const val ENABLE_DEBUG_LOGS = false
```
**Expected**: All events should be filtered out in debug builds

### Test 2: Disable API Errors Only
```kotlin
const val ENABLE_DEBUG_LOGS = true
const val ENABLE_API_ERROR_LOGS = false
```
**Expected**: API errors filtered, other events allowed

### Test 3: Only Critical Errors
```kotlin
const val ENABLE_DEBUG_LOGS = true
val MINIMUM_LOG_LEVEL = SentryLevel.FATAL
```
**Expected**: Only FATAL level events allowed

## Verify in Sentry Dashboard

1. **Go to your Sentry project dashboard**
2. **Check the Issues page** - you should see fewer events when filtering is active
3. **Look for custom tags** on events that do get through:
   - `custom.filter: processed`
   - `build.type: debug`
   - `app.version: [your version]`

## Reset After Testing

Remember to reset the configuration for production:
```kotlin
const val ENABLE_DEBUG_LOGS = false
const val ENABLE_RELEASE_LOGS = true
val MINIMUM_LOG_LEVEL = SentryLevel.WARNING
```

## Troubleshooting

**No filtering logs appearing?**
- Make sure `ENABLE_DEBUG_LOGS = true`
- Check that the app is using the debug build variant
- Verify Sentry is initializing (look for "Sentry initialization completed" log)

**Events still appearing in Sentry dashboard?**
- Check that you're looking at the correct project
- Verify the filtering logic matches your test scenario
- Remember there might be a delay before events appear in the dashboard

**App crashes on startup?**
- Check for compilation errors
- Verify all imports are correct
- Look for stack traces in Android logs
