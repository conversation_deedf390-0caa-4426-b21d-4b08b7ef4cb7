Appio Android
=============

# Release:

--- 

### Scripted release

Just call: `./scripts/release.sh`

**Requirements:** Install https://fastlane.tool via `brew install fastlane`

```shell
./gradlew clean bundleRelease

cd app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib
zip -r native-debug-symbols.zip .
cd -
```

```
fastlane supply \
  --aab ./app/build/outputs/bundle/release/app-release.aab \
  --track production \
  --json_key ./.keys/google-play-store.json \
  --package_name so.appio.app \
  --mapping_paths \
    ./app/build/outputs/mapping/release/mapping.txt \
    ./app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib/native-debug-symbols.zip
```

Or use `fastlane android deploy` to use `./fastlane/Fastfile` definition.

---

## Manual

### Generate app
1. Build: `./gradlew clean bundleRelease`
2. Upload: `app/build/outputs/bundle/release/app-release.aab` to Google Play Console
3. Locate debug symbols: `app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib`
4. Zip debug symbols `zip -r native-debug-symbols.zip .` (WARNING: can't use MacOS Archive feature because it adds __MACOSX folder)
5. Upload debug symbols to Google Play Console: click 3 dots next to Release Details

NOTE: Mapping symbols are uploaded automatically by Gradle plugin. But just in case: `app/build/outputs/mapping/release/mapping.txt`

---

## Release build on testing physical device
1. Build: `Build > Generate Signed App Bundle or APK > APK > "release"`
2. list devices: `abd devices`  (e.g. `R5CW42DPPZA`)
3. uninstall debug app `adb -s R5CW42DPPZA uninstall so.appio.app`
4. install `adb -s R5CW42DPPZA install -r app/release/app-release.apk` or `app/build/outputs/apk/release/app-release.apk`


## Requirements

Required files for Prod:
- `app/src/release/google-services.prod.json`
- `.keys/firebase-prod.json`   (only the `client_email`, `private_key` values are needed)

For API calls the build expects an `API_AUTH_TOKEN` entry in your `local.properties` file (which is ignored by Git):
`API_AUTH_TOKEN=your_token_here`

**Note**
- `.keys/appio-production.jks` is required to generate SSH key for app links. It is not required to exist on server.


---


# App Links

To associated website link with app, Android uses [App Links](https://developer.android.com/training/app-links/verify-android-applinks)

Each domain has to serve: `https://app.appio.so/.well-known/assetlinks.json`

## sha256_cert_fingerprints For development:
```
/opt/homebrew/opt/openjdk@17/bin/keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```
`3C:FB:F9:67:A0:CC:A6:02:44:C0:48:2D:E0:A1:18:D5:CA:FA:B4:BB:EE:41:9F:52:DB:8E:80:AF:F1:91:10:4D`

## sha256_cert_fingerprints For production:

1. Generate `.keys/appio-production.jks` via Android Studio: 
```
Build > Generate Signed Bundle ok APK > Android App Bundle > Next > Create new... > Next > Finish

Key store path: .keys/appio-production.jks  (from project root, ../.keys/... in local.properties)
Passwords: appio1
Key:
Alias:               appio
Password:            appio1
First and Last name: Appio
Organisation Unit:   Appio
Organisation:        Appio
City or Locality:    Edinburgh
State or Province:   Scotland
Country Code:        GB
```

2. Get SHA-256 fingerprint:
```
/opt/homebrew/opt/openjdk@17/bin/keytool -list -v -keystore .keys/appio-production.jks -alias appio
```
`6E:A0:4E:B6:3B:25:85:64:4A:69:5B:32:B7:AF:1D:C8:0E:1A:FC:49:C3:9D:2C:68:58:8B:1E:DE:95:FC:FC:AD`

## Test App Links

```bash
# list devices
adb devices

# pick ID f.e. R5CW42DPPZA
adb -s R5CW42DPPZA shell am start -a android.intent.action.VIEW -d "https://app.appio.so/android/?s=123"
```


---


# Custom URL Scheme

Registered `appio://`


---


# App Referrer

The `referrer` value us HTML encoded and if it contains query string, its values are also HTML encoded.

Example:
`https://play.google.com/store/apps/details?id=so.appio.app&referrer=service%3Ddemo_svc_01jvtpgft2ztw03dgdffvdx72w%26user%3D01JWDXJF3YM3FRFR1DQHGG2RM8%253Ahi%2540appio.so`

Will decode into:
```
service = demo_svc_01jvtpgft2ztw03dgdffvdx72w
user = 01JWDXJF3YM3FRFR1DQHGG2RM8:<EMAIL>
```

## Testing

- Build signed release build:
  `Build > Generate Signed Bundle ok APK > Android App Bundle > Next > ... > Next > Finish`
  or
  `./gradlew clean bundleRelease` this will create file in `app/build/outputs/bundle/release/app-release.aab`
- [Google Play Console → Test and release → Testing → Internal Testing](https://play.google.com/console/u/3/developers/8700420525321538598/app/4975909096083584444/tracks/internal-testing)
- Create a new release → Upload app bundle 
- Uninstall debug app: `adb -s R5CW42DPPZA uninstall so.appio.app`
- Install test app: https://play.google.com/store/apps/details?id=so.appio.app&referrer=service%3Ddemo_svc_01jyp23kdt07ssape2wsv3cm9q%26user%3Dskusam-referrer

---


# Widgets

- [Glance Widgets tutorial](https://katiebarnett.dev/talks/widgetsaresohotrightnow/)
- [GlanceStateDefinition tutorial](https://proandroiddev.com/widgets-with-glance-beyond-string-states-2dcc4db2f76c)


---


# Notifications

- [REST documentation](https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages)
- [Response codes and errors](https://firebase.google.com/docs/reference/fcm/rest/v1/ErrorCode)


---

# Components

https://m3.material.io/components


---
