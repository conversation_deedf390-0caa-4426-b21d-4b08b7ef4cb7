<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CAMERA"/>

    <!-- Camera hardware feature declaration for ChromeOS compatibility -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Appio"
        android:networkSecurityConfig="@xml/network_security_config">

        <!-- android:launchMode="singleTop is recommended for deep linking -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Appio.Splash"
            android:windowLayoutInDisplayCutoutMode="default">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Custom URL scheme -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="appio" />
            </intent-filter>

            <!-- Deep link intent filter (add this!) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"
                    android:host="app.appio.so"
                    android:pathPrefix="/android" /> <!-- empty or missing pathPrefix means all urls -->
            </intent-filter>
        </activity>

        <service
            android:name=".utils.FirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- TODO: Live activity -->
        <!-- type should be: dataSync or mediaPlayback or location: require user permissions -->
<!--        <service-->
<!--            android:name=".utils.LiveNotificationService"-->
<!--            android:exported="false"-->
<!--            android:foregroundServiceType="shortService" />-->

        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
             See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification_bell" />

        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
<!--        <meta-data-->
<!--            android:name="com.google.firebase.messaging.default_notification_color"-->
<!--            android:resource="@android:color/black" />-->

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id"/>

        <!-- Sentry Configuration -->
        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data
            android:name="io.sentry.dsn"
            android:value="https://<EMAIL>/4509572751556688" />

        <!-- Add data like request headers, user ip address and device name, see https://docs.sentry.io/platforms/android/data-management/data-collected/ for more info -->
        <meta-data android:name="io.sentry.send-default-pii" android:value="true" />

        <!-- Enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data
            android:name="io.sentry.traces.user-interaction.enable"
            android:value="true" />

        <!-- Enable screenshot for crashes -->
        <meta-data
            android:name="io.sentry.attach-screenshot"
            android:value="true" />

        <!-- Enable view hierarchy for crashes -->
        <meta-data
            android:name="io.sentry.attach-view-hierarchy"
            android:value="true" />

        <!-- Enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data
            android:name="io.sentry.traces.sample-rate"
            android:value="0.1" />

        <!-- Record session replays for 100% of errors and 10% of sessions -->
        <meta-data
            android:name="io.sentry.session-replay.on-error-sample-rate"
            android:value="1.0" />
        <meta-data
            android:name="io.sentry.session-replay.session-sample-rate"
            android:value="0.1" />

        <!-- Session replay masking configuration -->
        <!-- Disable text masking to capture actual text content in session replays -->
        <meta-data
            android:name="io.sentry.session-replay.mask-all-text"
            android:value="false" />
        <!-- Disable image masking to capture actual images in session replays -->
        <meta-data
            android:name="io.sentry.session-replay.mask-all-images"
            android:value="false" />

        <!-- Glance Widget Receiver -->
        <receiver
            android:name=".widgets.AppioWidgetReceiver"
            android:exported="true"
            android:label="Appio Widget">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appio_widget_info" />
        </receiver>

        <!-- Glance Widget Configuration Activity -->
        <activity
            android:name=".widgets.AppioWidgetConfigActivity"
            android:exported="false"
            android:launchMode="singleInstance"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:taskAffinity="">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
            </intent-filter>
        </activity>
    </application>

</manifest>