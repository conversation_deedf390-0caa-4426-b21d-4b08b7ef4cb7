package so.appio.app

import io.sentry.SentryOptions
import io.sentry.SentryOptionsConfiguration

class MySentryOptionsConfig : SentryOptionsConfiguration<SentryOptions> {
    override fun configure(options: SentryOptions) {
        // Filter out events
        options.setBeforeSend { event, _ ->
            val ex = event.throwable
            if (ex is IllegalStateException) {
                return@setBeforeSend null // Ignore
            }

            // You can modify the event here too
            event.setTag("custom.filter", "active")
            event
        }

        // Optional: Enable debug logging
        options.isDebug = BuildConfig.DEBUG
    }
}