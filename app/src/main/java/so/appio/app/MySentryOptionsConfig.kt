package so.appio.app

import android.util.Log
import io.sentry.SentryEvent
import io.sentry.SentryLevel
import io.sentry.SentryOptions
import io.sentry.SentryOptionsConfiguration

class MySentryOptionsConfig : SentryOptionsConfiguration<SentryOptions> {

    companion object {
        private const val TAG = "MySentryOptionsConfig"

        // Control flags for different types of logging
        private const val ENABLE_DEBUG_LOGS = false // Set to true to enable debug logs in debug builds
        private const val ENABLE_RELEASE_LOGS = true // Set to false to disable all logs in release builds
        private const val ENABLE_API_ERROR_LOGS = true // Set to false to disable API error logs
        private const val ENABLE_DATABASE_ERROR_LOGS = true // Set to false to disable database error logs
        private const val ENABLE_WIDGET_ERROR_LOGS = true // Set to false to disable widget error logs
        private const val ENABLE_CRASH_LOGS = true // Set to false to disable crash logs
        private const val ENABLE_PERFORMANCE_LOGS = true // Set to false to disable performance logs

        // Minimum log level to send (DEBUG, INFO, WARNING, ERROR, FATAL)
        private val MINIMUM_LOG_LEVEL = SentryLevel.WARNING
    }

    override fun configure(options: SentryOptions) {
        Log.d(TAG, "Configuring Sentry options with custom filtering")

        // Set debug mode based on build type
        options.isDebug = BuildConfig.DEBUG && ENABLE_DEBUG_LOGS

        // Configure comprehensive event filtering
        options.setBeforeSend { event, hint ->
            filterSentryEvent(event, hint)
        }

        // Configure breadcrumb filtering
        options.setBeforeBreadcrumb { breadcrumb, hint ->
            filterSentryBreadcrumb(breadcrumb, hint)
        }

        Log.d(TAG, "Sentry options configured successfully")
    }

    /**
     * Filter Sentry events before they are sent to the server.
     * Return null to prevent the event from being sent.
     */
    private fun filterSentryEvent(event: SentryEvent, hint: Any?): SentryEvent? {
        try {
            // Check if logging is enabled for current build type
            if (BuildConfig.DEBUG && !ENABLE_DEBUG_LOGS) {
                Log.d(TAG, "Filtering out event in debug build (debug logs disabled)")
                return null
            }

            if (!BuildConfig.DEBUG && !ENABLE_RELEASE_LOGS) {
                Log.d(TAG, "Filtering out event in release build (release logs disabled)")
                return null
            }

            // Filter by log level
            if (event.level != null && event.level!!.ordinal < MINIMUM_LOG_LEVEL.ordinal) {
                Log.d(TAG, "Filtering out event with level ${event.level} (below minimum ${MINIMUM_LOG_LEVEL})")
                return null
            }

            // Filter by error type and tags
            val errorType = event.getTag("error.type")
            val errorCategory = event.getTag("error.category")

            when (errorType) {
                "api_error" -> {
                    if (!ENABLE_API_ERROR_LOGS) {
                        Log.d(TAG, "Filtering out API error event")
                        return null
                    }
                }
                "database_error" -> {
                    if (!ENABLE_DATABASE_ERROR_LOGS) {
                        Log.d(TAG, "Filtering out database error event")
                        return null
                    }
                }
                "widget_error" -> {
                    if (!ENABLE_WIDGET_ERROR_LOGS) {
                        Log.d(TAG, "Filtering out widget error event")
                        return null
                    }
                }
                "uncaught_exception" -> {
                    if (!ENABLE_CRASH_LOGS) {
                        Log.d(TAG, "Filtering out crash event")
                        return null
                    }
                }
            }

            // Filter by exception type
            val exception = event.throwable
            if (exception != null && shouldIgnoreException(exception)) {
                Log.d(TAG, "Filtering out ignored exception: ${exception.javaClass.simpleName}")
                return null
            }

            // Filter performance events
            if (event.transaction != null && !ENABLE_PERFORMANCE_LOGS) {
                Log.d(TAG, "Filtering out performance event")
                return null
            }

            // Add custom tags to allowed events
            event.setTag("custom.filter", "processed")
            event.setTag("build.type", if (BuildConfig.DEBUG) "debug" else "release")
            event.setTag("app.version", BuildConfig.VERSION_NAME)

            Log.d(TAG, "Allowing event: ${event.eventId} (${errorType ?: "unknown_type"})")
            return event

        } catch (e: Exception) {
            Log.e(TAG, "Error in filterSentryEvent", e)
            // In case of filtering error, allow the event to be sent
            return event
        }
    }

    /**
     * Filter Sentry breadcrumbs before they are added.
     * Return null to prevent the breadcrumb from being added.
     */
    private fun filterSentryBreadcrumb(breadcrumb: io.sentry.Breadcrumb, hint: Any?): io.sentry.Breadcrumb? {
        try {
            // Filter breadcrumbs in debug mode if debug logs are disabled
            if (BuildConfig.DEBUG && !ENABLE_DEBUG_LOGS) {
                return null
            }

            // Filter breadcrumbs in release mode if release logs are disabled
            if (!BuildConfig.DEBUG && !ENABLE_RELEASE_LOGS) {
                return null
            }

            // You can add more specific breadcrumb filtering here
            // For example, filter out certain categories or messages

            return breadcrumb

        } catch (e: Exception) {
            Log.e(TAG, "Error in filterSentryBreadcrumb", e)
            // In case of filtering error, allow the breadcrumb
            return breadcrumb
        }
    }

    /**
     * Determine if an exception should be ignored based on its type or message.
     */
    private fun shouldIgnoreException(exception: Throwable): Boolean {
        return when (exception) {
            // Add specific exception types to ignore
            is IllegalStateException -> {
                // You can add more specific filtering based on message
                exception.message?.contains("getSplashScreen") == true
            }
            is NoSuchMethodError -> {
                // Filter out known Android version compatibility issues
                exception.message?.contains("getSplashScreen") == true
            }
            // Add more exception types as needed
            else -> false
        }
    }
}