package so.appio.app

import android.app.Application
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import so.appio.app.data.database.DatabaseManager
import so.appio.app.network.APIClient
import so.appio.app.data.repository.DeviceRepository
import so.appio.app.utils.DeviceManager
import so.appio.app.utils.GooglePlayServices
import so.appio.app.utils.ImageCacheManager
import so.appio.app.utils.NotificationChannels
import so.appio.app.utils.ServiceRefreshWorker
import so.appio.app.utils.SentryErrorHandler
import io.sentry.Sentry
import io.sentry.SentryEvent
import io.sentry.SentryLevel
import io.sentry.android.core.SentryAndroid

class MyApplication : Application() {

    companion object {
        internal const val TAG = "LOG:MyApplication"
        internal val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

        // Sentry filtering configuration
        private const val ENABLE_DEBUG_LOGS = false // Set to true to enable debug logs in debug builds
        private const val ENABLE_RELEASE_LOGS = true // Set to false to disable all logs in release builds
        private const val ENABLE_API_ERROR_LOGS = true // Set to false to disable API error logs
        private const val ENABLE_DATABASE_ERROR_LOGS = true // Set to false to disable database error logs
        private const val ENABLE_WIDGET_ERROR_LOGS = true // Set to false to disable widget error logs
        private const val ENABLE_CRASH_LOGS = true // Set to false to disable crash logs
        private const val ENABLE_PERFORMANCE_LOGS = true // Set to false to disable performance logs

        // Minimum log level to send (DEBUG, INFO, WARNING, ERROR, FATAL)
        private val MINIMUM_LOG_LEVEL = SentryLevel.WARNING
    }

    // Single instances for dependency injection
    lateinit var deviceRepository: DeviceRepository
        private set
    lateinit var deviceManager: DeviceManager
        private set

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate called")

        configureSentry()

        // Initialize core services
        GooglePlayServices.initializeFirebase(this)
        ImageCacheManager.initialize(this)
        DatabaseManager.initialize(this)
        APIClient.initialize(this)
        NotificationChannels.createDefaultNotificationChannel(this)

        // Initialize device-related components
        deviceRepository = DeviceRepository(this)
        deviceRepository.initialize()
        deviceManager = DeviceManager(this)

        // Do async operations
        createServiceNotificationChannels()
        checkDeviceSync()

        // Start periodic service refresh
        ServiceRefreshWorker.startAutoRefresh(this)
    }

    /**
     * Creates notification channels for all stored Service entities.
     * This is called after DatabaseManager initialization to ensure database access is available.
     */
    private fun createServiceNotificationChannels() {
        applicationScope.launch {
            try {
                Log.d(TAG, "Creating notification channels for stored services")

                val serviceRepository = DatabaseManager.getServiceRepository()
                val services = serviceRepository.getAllServicesList()

                services.forEach { service ->
                    NotificationChannels.createChannelForService(
                        context = this@MyApplication,
                        serviceId = service.id,
                        serviceTitle = service.title
                    )
                    Log.d(TAG, "Created notification channel for service: ${service.title} (${service.id})")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error creating service notification channels", e)
            }
        }
    }

    /**
     * Check if device data is out of sync and sync if needed.
     * This runs asynchronously after DeviceRepository is initialized.
     */
    private fun checkDeviceSync() {
        applicationScope.launch {
            try {
                Log.d(TAG, "Checking device sync status")
                deviceRepository.checkAndSyncIfNeeded()
                Log.d(TAG, "Device sync check completed")
            } catch (e: Exception) {
                Log.e(TAG, "Error checking device sync", e)
            }
        }
    }

    /**
     * Refresh all services data when app comes to foreground.
     * This ensures data stays fresh when user returns to the app.
     */
    fun refreshServices() {
        Log.d(TAG, "Triggering service refresh on app resume")
        ServiceRefreshWorker.refreshServicesForEvent(this, ServiceRefreshWorker.REASON_APP_RESUME)
    }

    /**
     * Configure Sentry monitoring for crash reporting and error tracking.
     * Manual initialization with comprehensive filtering control.
     */
    private fun configureSentry() {
        try {
            Log.d(TAG, "Initializing Sentry with custom filtering")

            // Manual initialization with filtering
            SentryAndroid.init(this) { options ->
                // Basic configuration
                options.dsn = "https://<EMAIL>/4509572751556688"
                options.isDebug = BuildConfig.DEBUG && ENABLE_DEBUG_LOGS
                options.environment = if (BuildConfig.DEBUG) "debug" else "release"
                options.release = BuildConfig.VERSION_NAME

                // Performance monitoring
                options.tracesSampleRate = if (ENABLE_PERFORMANCE_LOGS) 0.1 else 0.0

                // Session replay
                options.sessionReplay.onErrorSampleRate = 1.0
                options.sessionReplay.sessionSampleRate = 0.1

                // Additional features
                options.isSendDefaultPii = true
                options.isAttachScreenshot = true
                options.isAttachViewHierarchy = true
                options.isEnableUserInteractionTracing = true

                // Configure comprehensive event filtering
                options.setBeforeSend { event, hint ->
                    filterSentryEvent(event, hint)
                }

                // Configure breadcrumb filtering
                options.setBeforeBreadcrumb { breadcrumb, hint ->
                    filterSentryBreadcrumb(breadcrumb, hint)
                }

                Log.d(TAG, "Sentry options configured with filtering")
            }

            // Install global exception handler for uncaught exceptions
            SentryErrorHandler.installGlobalExceptionHandler()

            // Add additional context
            Sentry.configureScope { scope ->
                scope.setTag("app.component", "MyApplication")
                scope.setTag("build.type", if (BuildConfig.DEBUG) "debug" else "release")

                // Add custom context, showed in UI as "Additional Data"
                scope.setExtra("app.version", BuildConfig.VERSION_NAME)
                scope.setExtra("app.version.code", BuildConfig.VERSION_CODE.toString())
                scope.setExtra("initialization.timestamp", System.currentTimeMillis().toString())
            }

            Log.d(TAG, "Sentry initialization completed with filtering enabled")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure Sentry", e)
        }
    }

    /**
     * Filter Sentry events before they are sent to the server.
     * Return null to prevent the event from being sent.
     */
    private fun filterSentryEvent(event: SentryEvent, hint: Any?): SentryEvent? {
        try {
            // Check if logging is enabled for current build type
            if (BuildConfig.DEBUG && !ENABLE_DEBUG_LOGS) {
                Log.d(TAG, "Filtering out event in debug build (debug logs disabled)")
                return null
            }

            if (!BuildConfig.DEBUG && !ENABLE_RELEASE_LOGS) {
                Log.d(TAG, "Filtering out event in release build (release logs disabled)")
                return null
            }

            // Filter by log level
            if (event.level != null && event.level!!.ordinal < MINIMUM_LOG_LEVEL.ordinal) {
                Log.d(TAG, "Filtering out event with level ${event.level} (below minimum ${MINIMUM_LOG_LEVEL})")
                return null
            }

            // Filter by error type and tags
            val errorType = event.getTag("error.type")

            when (errorType) {
                "api_error" -> {
                    if (!ENABLE_API_ERROR_LOGS) {
                        Log.d(TAG, "Filtering out API error event")
                        return null
                    }
                }
                "database_error" -> {
                    if (!ENABLE_DATABASE_ERROR_LOGS) {
                        Log.d(TAG, "Filtering out database error event")
                        return null
                    }
                }
                "widget_error" -> {
                    if (!ENABLE_WIDGET_ERROR_LOGS) {
                        Log.d(TAG, "Filtering out widget error event")
                        return null
                    }
                }
                "uncaught_exception" -> {
                    if (!ENABLE_CRASH_LOGS) {
                        Log.d(TAG, "Filtering out crash event")
                        return null
                    }
                }
            }

            // Filter by exception type
            val exception = event.throwable
            if (exception != null && shouldIgnoreException(exception)) {
                Log.d(TAG, "Filtering out ignored exception: ${exception.javaClass.simpleName}")
                return null
            }

            // Filter performance events
            if (event.transaction != null && !ENABLE_PERFORMANCE_LOGS) {
                Log.d(TAG, "Filtering out performance event")
                return null
            }

            // Add custom tags to allowed events
            event.setTag("custom.filter", "processed")
            event.setTag("build.type", if (BuildConfig.DEBUG) "debug" else "release")
            event.setTag("app.version", BuildConfig.VERSION_NAME)

            Log.d(TAG, "Allowing event: ${event.eventId} (${errorType ?: "unknown_type"})")
            return event

        } catch (e: Exception) {
            Log.e(TAG, "Error in filterSentryEvent", e)
            // In case of filtering error, allow the event to be sent
            return event
        }
    }