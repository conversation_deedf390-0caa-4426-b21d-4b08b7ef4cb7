# Sentry Manual Setup

This document explains the clean manual Sentry initialization setup in the Appio Android app.

## Overview

Sentry is manually initialized in `MyApplication.kt` using `SentryAndroid.init()`. Auto-initialization is disabled to avoid conflicts and provide complete control over configuration.

## Configuration

### AndroidManifest.xml
Only contains the auto-init disable flag:
```xml
<!-- Sentry Configuration -->
<!-- Disable auto-initialization - using manual initialization in MyApplication.kt -->
<meta-data
    android:name="io.sentry.auto-init"
    android:value="false" />
```

### MyApplication.kt
Contains all Sentry configuration:
```kotlin
// Manual initialization
SentryAndroid.init(this) { options ->
    // Basic configuration
    options.dsn = "https://<EMAIL>/4509572751556688"
    options.isDebug = BuildConfig.DEBUG
    options.environment = if (BuildConfig.DEBUG) "debug" else "release"
    options.release = BuildConfig.VERSION_NAME
    
    // Performance monitoring
    options.tracesSampleRate = 0.1
    
    // Session replay
    options.sessionReplay.onErrorSampleRate = 1.0
    options.sessionReplay.sessionSampleRate = 0.1
    
    // Additional features
    options.isSendDefaultPii = true
    options.isAttachScreenshot = true
    options.isAttachViewHierarchy = true
    options.isEnableUserInteractionTracing = true
    
    // Add custom filtering here if needed:
    // options.setBeforeSend { event, hint ->
    //     // Your custom filtering logic
    //     event
    // }
}
```

## Adding Custom Filtering

To add custom filtering, uncomment and modify the `setBeforeSend` block:

```kotlin
options.setBeforeSend { event, hint ->
    // Example: Filter out debug events
    if (BuildConfig.DEBUG) {
        return@setBeforeSend null // Don't send debug events
    }
    
    // Example: Filter by exception type
    if (event.throwable is IllegalStateException) {
        return@setBeforeSend null // Don't send IllegalStateException
    }
    
    // Example: Add custom tags
    event.setTag("custom.processed", "true")
    
    // Return the event to send it
    event
}
```

## Benefits of Manual Setup

1. **No Conflicts**: Single source of truth for all configuration
2. **Complete Control**: All options are programmatically controlled
3. **Easy Filtering**: Simple to add custom filtering logic
4. **Maintainable**: All Sentry code is in one place
5. **Testable**: Easy to modify configuration for testing

## Features Enabled

- **Error Reporting**: All uncaught exceptions and manual error reports
- **Performance Monitoring**: 10% sampling rate for performance traces
- **Session Replay**: 100% on errors, 10% on normal sessions
- **Screenshots**: Attached to crash reports
- **View Hierarchy**: Attached to crash reports
- **User Interactions**: Breadcrumbs for user interactions
- **PII Data**: Enabled (be careful with sensitive data)

## Testing

To test Sentry is working:
1. Build and run the app
2. Check logs for "Sentry initialization completed"
3. Trigger an error or crash
4. Check your Sentry dashboard for the event

## Environment Configuration

- **Debug builds**: `environment = "debug"`, debug logging enabled
- **Release builds**: `environment = "release"`, debug logging disabled
- **Version tracking**: Uses `BuildConfig.VERSION_NAME` as release identifier
